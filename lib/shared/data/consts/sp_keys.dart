abstract class SPKeys {
  static const String restrict = 'restrictModel';
  static const String user = 'user';
  static const String refreshToken = 'refreshToken';
  static const String accessToken = 'accessToken';
  static const String timezone = 'timezone';
  static const String appTheme = 'appTheme';
  static const String appLocale = 'appLocale';
  static const String roomInfo = 'roomInfo';
  static const String conversationsTimestamp = 'conversationsTimestamp';
  static const String appConfig = 'appConfig';

  // Permission reminder keys
  static const String locationPermissionDeniedTime =
      'locationPermissionDeniedTime';
  static const String locationPermissionDeniedCount =
      'locationPermissionDeniedCount';
  static const String notificationPermissionDeniedTime =
      'notificationPermissionDeniedTime';
  static const String notificationPermissionDeniedCount =
      'notificationPermissionDeniedCount';
  static const String lastPermissionReminderTime = 'lastPermissionReminderTime';
}

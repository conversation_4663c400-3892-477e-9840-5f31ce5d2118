import 'dart:async';
import 'dart:io';

import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/purchase/data/datasources/local_product_datasource.dart';
import 'package:flutter_audio_room/features/purchase/data/datasources/remote_product_datasource.dart';
import 'package:flutter_audio_room/features/purchase/data/repositories/product_repository_impl.dart';
import 'package:flutter_audio_room/features/purchase/data/repositories/purchase_repository_impl.dart';
import 'package:flutter_audio_room/features/purchase/domain/entities/purchase_item.dart';
import 'package:flutter_audio_room/features/purchase/domain/repositories/i_product_repository.dart';
import 'package:flutter_audio_room/features/purchase/domain/repositories/i_purchase_repository.dart';
import 'package:flutter_audio_room/features/purchase/presentation/providers/purchase_process_state_provider.dart';
import 'package:flutter_audio_room/shared/data/remote/network_service.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscription_purchase_provider.g.dart';

@riverpod
class SubscriptionPurchase extends _$SubscriptionPurchase {
  StreamSubscription<PurchaseDetails>? _purchaseSubscription;
  IPurchaseRepository? _purchaseRepository;

  @override
  FutureOr<List<PurchaseItem>> build() async {
    final purchaseRepository = _createPurchaseRepository();
    _purchaseRepository = purchaseRepository;

    ref.onDispose(() {
      _purchaseSubscription?.cancel();
      purchaseRepository.dispose();
    });

    // 1. 设置购买状态监听
    _setupPurchaseListener();

    // 2. 初始化仓库并获取产品
    return _initializeAndFetchSubscriptions();
  }

  IProductRepository _createProductRepository() {
    final networkService = getIt<NetworkService>();
    final remoteDataSource = RemoteProductDatasource(networkService);
    final localDataSource = LocalProductDataSource();

    return ProductRepositoryImpl(
      localDataSource: localDataSource,
      remoteDataSource: remoteDataSource,
    );
  }

  PurchaseRepositoryImpl _createPurchaseRepository() {
    final inAppPurchase = InAppPurchase.instance;
    final productRepo = _createProductRepository();
    return PurchaseRepositoryImpl(
      inAppPurchase: inAppPurchase,
      productRepository: productRepo,
    );
  }

  /// 设置购买状态监听器
  void _setupPurchaseListener() {
    _purchaseSubscription =
        _purchaseRepository?.purchaseStatusStream.listen(_handlePurchaseUpdate);
  }

  /// 处理购买状态更新
  void _handlePurchaseUpdate(PurchaseDetails purchaseDetails) async {
    final products = await future;
    final productIds = products.map((e) => e.productId).toList();

    LogUtils.d('productIds: $productIds', tag: 'SubscriptionPurchase');
    LogUtils.d('purchaseDetails.productID: ${purchaseDetails.productID}',
        tag: 'SubscriptionPurchase');

    if (purchaseDetails.productID.isNotEmpty &&
        !productIds.contains(purchaseDetails.productID)) {
      return;
    }
    
    switch (purchaseDetails.status) {
      case PurchaseStatus.pending:
        // _setPurchaseState(PurchaseProcessState.purchasing);
        break;
      case PurchaseStatus.purchased:
        _setPurchaseState(PurchaseProcessState.purchased);
        await _handlePurchaseVerification(purchaseDetails);
        break;
      case PurchaseStatus.restored:
        _setPurchaseState(PurchaseProcessState.purchased);
        await _handlePurchaseVerification(purchaseDetails);
        break;
      case PurchaseStatus.error:
        _setPurchaseState(PurchaseProcessState.failed);
        LogUtils.e('purchaseDetails: ${purchaseDetails.error}',
            tag: 'SubscriptionPurchase');
        LoadingUtils.showToast(purchaseDetails.error?.details ??
            'Purchase failed, please try again later');
        break;
      case PurchaseStatus.canceled:
        _setPurchaseState(PurchaseProcessState.canceled);
        break;
    }
  }

  /// 处理购买验证
  Future<void> _handlePurchaseVerification(
      PurchaseDetails purchaseDetails) async {
    try {
      _setPurchaseState(PurchaseProcessState.verifying);

      final result = await _purchaseRepository?.verifyPurchase(purchaseDetails,
          isSubscription: true);

      result?.fold(
        (error) {
          _setPurchaseState(PurchaseProcessState.failed);
        },
        (data) {
          _setPurchaseState(PurchaseProcessState.verified);
        },
      );
    } catch (e) {
      _setPurchaseState(PurchaseProcessState.failed);
    }

    if (purchaseDetails.pendingCompletePurchase) {
      await _purchaseRepository?.completePurchase(purchaseDetails);
    }
  }

  /// 初始化仓库并获取订阅产品列表
  Future<List<PurchaseItem>> _initializeAndFetchSubscriptions() async {
    // 初始化仓库
    final result = await _purchaseRepository?.initialize();
    if (result == null) {
      throw Exception('Failed to initialize purchase repository');
    }

    return result.fold(
      (error) => throw error,
      (_) async {
        // 获取订阅产品列表
        final productsResult = await _purchaseRepository
            ?.initSubscriptions()
            .timeout(const Duration(seconds: 20));
        if (productsResult == null) {
          throw Exception('Failed to fetch subscription products');
        }
        return productsResult.fold(
          (error) => throw error,
          (products) {
            products.sort((a, b) =>
                a.product.productOrder.compareTo(b.product.productOrder));
            return products;
          },
        );
      },
    );
  }

  /// 设置购买状态
  void _setPurchaseState(PurchaseProcessState newState) {
    ref.read(purchaseProcessStateProvider.notifier).state = newState;
  }

  /// 购买订阅
  Future<void> purchaseSubscription(String productId) async {
    if (state.value == null) return;

    if (purchaseState == PurchaseProcessState.purchasing) {
      return;
    }

    final userIdFuture = ref.read(userIdProvider.future);
    final userUUIDFuture =
        Platform.isIOS ? ref.read(userUUIDProvider.future) : null;

    _setPurchaseState(PurchaseProcessState.purchasing);

    String? userId = await userIdFuture;

    if (Platform.isIOS && userUUIDFuture != null) {
      userId = await userUUIDFuture;
    }

    if (userId == null) {
      LoadingUtils.showToast('User UUID is null');
      _setPurchaseState(PurchaseProcessState.failed);

      // Track purchase failure
      await AnalyticsUtils.trackSubscriptionPurchase(
        subscriptionType: productId,
        duration: 'unknown',
        price: 0.0,
        currency: 'USD',
        transactionId: 'failed_${DateTime.now().millisecondsSinceEpoch}',
      );

      return;
    }

    final result = await _purchaseRepository?.purchaseProduct(
      productId,
      isSubscription: true,
      userId: userId,
    );

    result?.fold(
      (error) {
        _setPurchaseState(PurchaseProcessState.failed);
        LogUtils.e('Failed to purchase subscription: ${error.message}',
            tag: 'SubscriptionPurchase');
        LoadingUtils.showToast('Failed to purchase subscription');

        // Track purchase failure
        AnalyticsUtils.trackError(
          errorType: 'subscription_purchase_failed',
          errorMessage: error.message,
          errorCode: error.statusCode.toString(),
          context: 'SubscriptionPurchaseProvider.purchaseSubscription',
        );
      },
      (success) {
        LogUtils.d('start purchaseSubscription success: $success',
            tag: 'SubscriptionPurchase');
        // Note: Successful purchase tracking will be handled in the purchase verification
      },
    );
  }

  /// 恢复购买
  Future<void> restorePurchases() async {
    // 重置并设置状态
    resetPurchaseState();

    final result = await _purchaseRepository?.restorePurchases();

    result?.fold(
      (error) {
        _setPurchaseState(PurchaseProcessState.failed);
        LogUtils.e('Failed to restore purchases: ${error.message}',
            tag: 'SubscriptionPurchase');
      },
      (items) {
        _handleRestoreResult(items);
      },
    );
  }

  /// 处理恢复结果
  void _handleRestoreResult(List<PurchaseItem> items) {
    // 如果没有可恢复的购买，也显示消息
    LogUtils.d('待恢复列表长度: ${items.length}', tag: 'SubscriptionPurchase');
    LoadingUtils.showToast('No subscriptions found to restore');
  }

  /// 重置购买状态
  void resetPurchaseState() {
    _setPurchaseState(PurchaseProcessState.initial);
  }

  /// 返回当前购买过程状态
  PurchaseProcessState get purchaseState =>
      ref.read(purchaseProcessStateProvider);
}
